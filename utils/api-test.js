// API测试工具
// 用于测试 /cook/upload/photos 接口的请求格式

/**
 * 测试用的Base64图片数据（1x1像素的JPEG图片）
 */
const testBase64Image = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';

/**
 * 创建符合接口文档要求的测试数据
 */
function createTestUploadData() {
  const timestamp = new Date().getTime();
  const userId = wx.getStorageSync('userId') || 1; // 获取用户ID，默认为1

  return {
    userId: userId,
    files: [
      {
        fileName: `test_image_${timestamp}.jpg`,
        fileId: timestamp,
        fileSize: 1024, // 假设文件大小为1KB
        fileData: `data:image/jpeg;base64,${testBase64Image}`
      }
    ]
  };
}

/**
 * 测试API请求格式
 * @param {Object} apiConfig - API配置对象
 * @returns {Promise} 请求结果
 */
function testUploadPhotosAPI(apiConfig) {
  const testData = createTestUploadData();
  
  console.log('测试数据格式:', JSON.stringify(testData, null, 2));
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.uploadPhotos}`,
      method: 'POST',
      data: testData,
      header: {
        'content-type': 'application/json',
        'accept': 'application/json'
      },
      success: (res) => {
        console.log('API测试成功:', res);
        resolve(res);
      },
      fail: (error) => {
        console.error('API测试失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 验证请求数据格式是否符合接口文档
 * @param {Object} uploadData - 上传数据
 * @returns {Object} 验证结果
 */
function validateUploadData(uploadData) {
  const errors = [];
  
  // 检查根级别的files字段
  if (!uploadData.files || !Array.isArray(uploadData.files)) {
    errors.push('缺少files字段或files不是数组');
    return { valid: false, errors };
  }
  
  // 检查每个文件对象
  uploadData.files.forEach((file, index) => {
    const prefix = `files[${index}]`;
    
    // 必填字段检查
    if (!file.fileName) {
      errors.push(`${prefix}.fileName 是必填字段`);
    }
    
    if (!file.fileSize || typeof file.fileSize !== 'number' || file.fileSize <= 0) {
      errors.push(`${prefix}.fileSize 必须是大于0的数字`);
    }
    
    if (!file.fileData) {
      errors.push(`${prefix}.fileData 是必填字段`);
    } else if (!file.fileData.startsWith('data:image/')) {
      errors.push(`${prefix}.fileData 必须是完整的Data URL格式，以"data:image/"开头`);
    }
    
    // 可选字段检查
    if (file.fileId !== undefined && typeof file.fileId !== 'number') {
      errors.push(`${prefix}.fileId 如果提供，应该是数字类型`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 测试根据食材搜索菜谱接口
 * @param {Object} apiConfig - API配置对象
 * @param {String} ingredientNames - 食材名称，多个用逗号分隔
 * @param {Number} pageNum - 页码，默认1
 * @param {Number} pageSize - 每页大小，默认10
 * @returns {Promise} 请求结果
 */
function testSearchRecipesByIngredientsAPI(apiConfig, ingredientNames = '鸡蛋,包菜', pageNum = 1, pageSize = 10) {
  const url = `${apiConfig.baseUrl}${apiConfig.api.searchRecipesByIngredients}?ingredientNames=${encodeURIComponent(ingredientNames)}&pageNum=${pageNum}&pageSize=${pageSize}`;

  console.log('测试搜索菜谱API:', url);

  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('搜索菜谱API测试成功:', res);
        resolve(res);
      },
      fail: (error) => {
        console.error('搜索菜谱API测试失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 验证搜索菜谱响应数据格式
 * @param {Object} responseData - 响应数据
 * @returns {Object} 验证结果
 */
function validateSearchRecipesResponse(responseData) {
  const errors = [];

  // 检查基本结构
  if (responseData.code !== 0) {
    errors.push('响应code不正确，应该是0');
  }

  if (!responseData.msg) {
    errors.push('缺少msg字段');
  }

  if (!responseData.data) {
    errors.push('缺少data字段');
    return { valid: false, errors };
  }

  const data = responseData.data;

  // 检查分页信息
  if (!Array.isArray(data.list)) {
    errors.push('data.list应该是数组');
  }

  if (typeof data.total !== 'number') {
    errors.push('data.total应该是数字');
  }

  if (typeof data.pageNum !== 'number') {
    errors.push('data.pageNum应该是数字');
  }

  if (typeof data.pageSize !== 'number') {
    errors.push('data.pageSize应该是数字');
  }

  if (typeof data.pages !== 'number') {
    errors.push('data.pages应该是数字');
  }

  if (typeof data.hasNext !== 'boolean') {
    errors.push('data.hasNext应该是布尔值');
  }

  if (typeof data.hasPrevious !== 'boolean') {
    errors.push('data.hasPrevious应该是布尔值');
  }

  // 检查菜谱列表项
  if (data.list && data.list.length > 0) {
    data.list.forEach((recipe, index) => {
      const prefix = `data.list[${index}]`;

      if (!recipe.id) {
        errors.push(`${prefix}.id 是必填字段`);
      }

      if (!recipe.name) {
        errors.push(`${prefix}.name 是必填字段`);
      }

      if (!recipe.description) {
        errors.push(`${prefix}.description 是必填字段`);
      }

      if (typeof recipe.difficultyLevel !== 'number') {
        errors.push(`${prefix}.difficultyLevel 应该是数字`);
      }

      if (!recipe.difficultyLevelDesc) {
        errors.push(`${prefix}.difficultyLevelDesc 是必填字段`);
      }

      if (typeof recipe.servings !== 'number') {
        errors.push(`${prefix}.servings 应该是数字`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 测试获取菜谱详情接口
 * @param {Object} apiConfig - API配置对象
 * @param {Number} recipeId - 菜谱ID
 * @returns {Promise} 请求结果
 */
function testRecipeDetailAPI(apiConfig, recipeId = 1) {
  const url = `${apiConfig.baseUrl}${apiConfig.api.recipeDetail}/${recipeId}`;

  console.log('测试菜谱详情API:', url);

  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('菜谱详情API测试成功:', res);
        resolve(res);
      },
      fail: (error) => {
        console.error('菜谱详情API测试失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 验证菜谱详情响应数据格式
 * @param {Object} responseData - 响应数据
 * @returns {Object} 验证结果
 */
function validateRecipeDetailResponse(responseData) {
  const errors = [];

  // 检查基本结构
  if (responseData.code !== 0) {
    errors.push('响应code不正确，应该是0');
  }

  if (!responseData.msg) {
    errors.push('缺少msg字段');
  }

  if (!responseData.data) {
    errors.push('缺少data字段');
    return { valid: false, errors };
  }

  const data = responseData.data;

  // 检查菜谱基本信息
  if (!data.id) {
    errors.push('data.id 是必填字段');
  }

  if (!data.name) {
    errors.push('data.name 是必填字段');
  }

  if (!data.description) {
    errors.push('data.description 是必填字段');
  }

  if (typeof data.difficultyLevel !== 'number') {
    errors.push('data.difficultyLevel 应该是数字');
  }

  if (!data.difficultyLevelDesc) {
    errors.push('data.difficultyLevelDesc 是必填字段');
  }

  if (typeof data.servings !== 'number') {
    errors.push('data.servings 应该是数字');
  }

  // 检查食材列表
  if (!Array.isArray(data.ingredients)) {
    errors.push('data.ingredients 应该是数组');
  } else {
    data.ingredients.forEach((ingredient, index) => {
      const prefix = `data.ingredients[${index}]`;

      if (!ingredient.ingredientId) {
        errors.push(`${prefix}.ingredientId 是必填字段`);
      }

      if (!ingredient.ingredientName) {
        errors.push(`${prefix}.ingredientName 是必填字段`);
      }

      if (!ingredient.quantity) {
        errors.push(`${prefix}.quantity 是必填字段`);
      }

      if (!ingredient.type) {
        errors.push(`${prefix}.type 是必填字段`);
      }

      if (!ingredient.typeDesc) {
        errors.push(`${prefix}.typeDesc 是必填字段`);
      }
    });
  }

  // 检查制作步骤
  if (!Array.isArray(data.steps)) {
    errors.push('data.steps 应该是数组');
  } else {
    data.steps.forEach((step, index) => {
      const prefix = `data.steps[${index}]`;

      if (!step.id) {
        errors.push(`${prefix}.id 是必填字段`);
      }

      if (typeof step.stepOrder !== 'number') {
        errors.push(`${prefix}.stepOrder 应该是数字`);
      }

      if (!step.description) {
        errors.push(`${prefix}.description 是必填字段`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

module.exports = {
  createTestUploadData,
  testUploadPhotosAPI,
  validateUploadData,
  testBase64Image,
  testSearchRecipesByIngredientsAPI,
  validateSearchRecipesResponse,
  testRecipeDetailAPI,
  validateRecipeDetailResponse
};
