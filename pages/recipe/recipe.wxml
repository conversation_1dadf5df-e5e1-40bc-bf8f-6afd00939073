<!--pages/recipe/recipe.wxml-->
<navigation-bar title="{{pageTitle}}" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="container" scroll-y="true" enhanced="true" show-scrollbar="false">
  <!-- 搜索框 (仅在搜索模式下显示) -->
  <view class="search-section" wx:if="{{isSearchMode}}">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input class="search-input"
               placeholder="请输入菜名搜索"
               value="{{searchKeyword}}"
               bindinput="onSearchInput"
               bindconfirm="onSearchConfirm"
               focus="{{searchFocus}}" />
        <view class="search-btn" bindtap="onSearchConfirm">
          <text class="search-icon">🔍</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索结果 (仅在搜索模式下显示) -->
  <view class="search-results" wx:if="{{isSearchMode}}">
    <view class="search-hint" wx:if="{{searchResults.length === 0 && !isLoading}}">
    </view>

    <view class="search-recipe-list" wx:if="{{searchResults.length > 0}}">
      <view class="search-recipe-item" wx:for="{{searchResults}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="search-recipe-icon">
          <text class="search-recipe-emoji">🍽️</text>
        </view>
        <view class="search-recipe-content">
          <text class="search-recipe-title">{{item.name}}</text>
          <text class="search-recipe-desc">{{item.difficultyLevelDesc || '简单'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 已有食材标签 -->
  <view class="ingredients-section" wx:if="{{!isSearchMode && resultList.length > 0}}">
    <view class="ingredients-header">
      <text class="ingredients-label">已有食材：</text>
    </view>
    <view class="ingredients-tags">
      <view class="ingredient-tag" wx:for="{{resultList}}" wx:key="index">
        <text class="tag-text">{{item.name}}</text>
      </view>
      <view class="ingredient-tag add-more">
        <text class="tag-text">+1个</text>
      </view>
    </view>
  </view>

  <!-- 菜谱列表 -->
  <view class="recipe-list" wx:if="{{!isSearchMode && recipes.length > 0}}">
    <view class="recipe-card" wx:for="{{recipes}}" wx:key="id">
      <!-- 菜谱图片和基本信息 -->
      <view class="recipe-main">
        <view class="recipe-image">
          <text class="recipe-emoji">{{item.emoji || '🍽️'}}</text>
        </view>
        <view class="recipe-info">
          <view class="recipe-name">{{item.name}}</view>
          <view class="recipe-description">{{item.description}}</view>
          <view class="recipe-meta">
            <view class="meta-time">
              <text class="time-icon">⏰</text>
              <text class="time-text">{{item.cookingTime || '15'}}分钟</text>
            </view>
            <view class="meta-rating">
              <text class="rating-icon">⭐</text>
              <text class="rating-text">{{item.rating || '4.8'}}</text>
            </view>
            <view class="meta-views">
              <text class="views-icon">👥</text>
              <text class="views-text">{{item.viewCount || '1234'}}</text>
            </view>
          </view>
          <view class="recipe-stars">
            <text class="stars-text">☆☆☆☆☆</text>
          </view>
        </view>
      </view>

      <!-- 食材匹配度 -->
      <view class="ingredient-match">
        <view class="match-header">
          <text class="match-title">食材匹配度</text>
          <text class="match-ratio">{{item.matchInfo.matchedCount}}/{{item.matchInfo.totalNeeded}}</text>
        </view>
        <view class="match-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.matchInfo.matchPercentage}}%"></view>
          </view>
        </view>
        <view class="match-details">
          <text class="match-have">已有: {{item.matchInfo.matchedCount}}样</text>
          <text class="match-need">还需: {{item.matchInfo.needCount}}样</text>
        </view>
      </view>

      <!-- 查看详细做法按钮 -->
      <view class="recipe-action">
        <button class="detail-btn" bindtap="navigateToDetail" data-id="{{item.id}}">查看详细做法</button>
      </view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacer"></view>
</scroll-view>