// pages/profile/profile.js
const authManager = require('../../utils/auth.js');
const apiConfig = require('../../config/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    isLoading: false,
    showEditor: false,
    isSaving: false, // 保存状态
    tempUserInfo: {
      nickName: '',
      avatarUrl: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '智能烹饪助手 - 拍照识别食材，推荐美味菜谱',
      path: '/pages/index/index',
      imageUrl: '',
      success: function(res) {
        console.log('分享成功', res);
      },
      fail: function(res) {
        console.log('分享失败', res);
      }
    };
  },

  // 检查登录状态
  checkLoginStatus() {
    console.log('检查登录状态...');
    if (authManager.checkLoginStatus()) {
      const userInfo = authManager.getCurrentUser();
      console.log('用户已登录，用户信息:', userInfo);
      this.setData({ userInfo });
    } else {
      console.log('用户未登录');
      this.setData({ userInfo: null });
    }
  },

  // 登录函数 - 修复getUserProfile调用时机
  login() {
    if (this.data.isLoading) {
      return;
    }

    console.log('🚀 开始登录流程');

    // 第一步：立即获取用户信息（必须在用户直接点击事件中调用）
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: async (res) => {
        console.log('👤 获取用户信息成功:', res.userInfo);
        console.log('📝 详细信息:');
        console.log('  昵称:', res.userInfo.nickName);
        console.log('  头像:', res.userInfo.avatarUrl);
        console.log('  性别:', res.userInfo.gender);
        console.log('  国家:', res.userInfo.country);
        console.log('  省份:', res.userInfo.province);
        console.log('  城市:', res.userInfo.city);
        console.log('  语言:', res.userInfo.language);

        // 判断是否为测试数据
        if (res.userInfo.nickName === '微信用户' && res.userInfo.avatarUrl.includes('thirdwx.qlogo.cn')) {
          console.log('⚠️  注意：这是微信开发者工具的测试数据');
          console.log('   在真机上才能获取到真实的用户信息');
        } else {
          console.log('✅ 这可能是真实的用户信息');
        }

        // 开始加载状态
        this.setData({ isLoading: true });
        wx.showLoading({ title: '登录中...' });

        try {
          // 第二步：身份验证登录（传递用户信息）
          console.log('🔐 开始身份验证...');
          const loginResult = await authManager.wxLogin(res.userInfo);

          if (!loginResult.success) {
            throw new Error(loginResult.message || '身份验证失败');
          }

          console.log('✅ 身份验证成功');

          // 第三步：更新页面显示
          const userInfo = authManager.getCurrentUser();
          console.log('📱 最终用户信息:', userInfo);
          console.log('📝 最终详细信息:');
          console.log('  昵称:', userInfo.nickName);
          console.log('  头像:', userInfo.avatarUrl);

          this.setData({
            userInfo: userInfo,
            isLoading: false
          });

          wx.hideLoading();
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

        } catch (error) {
          console.error('❌ 登录过程失败:', error);
          this.setData({ isLoading: false });
          wx.hideLoading();
          wx.showToast({
            title: error.message || '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('❌ 用户拒绝授权:', error);
        wx.showToast({
          title: '需要授权才能登录',
          icon: 'none'
        });
      }
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          authManager.logout();
          this.setData({
            userInfo: null
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },



  // 调试功能 - 在控制台调用
  debugUserInfo() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.debugUserInfo();
  },

  // 测试用户信息获取 - 在控制台调用
  testGetUserProfile() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.testGetUserProfile();
  },

  // 模拟真实用户信息 - 在控制台调用
  simulateRealUserInfo() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.simulateRealUserInfo();
    
    // 更新页面显示
    const userInfo = authManager.getCurrentUser();
    this.setData({ userInfo });
  },

  // 清除测试数据 - 在控制台调用
  clearTestData() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.clearTestData();
    
    // 更新页面显示
    this.setData({ userInfo: null });
  },

  // 显示个人资料编辑器
  showProfileEditor() {
    if (!this.data.userInfo) {
      // 如果用户未登录，则提示登录
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    console.log('显示编辑器，当前用户信息:', this.data.userInfo);

    // 初始化临时用户信息，确保昵称正确传递
    const tempUserInfo = {
      nickName: this.data.userInfo.nickName || '',
      avatarUrl: this.data.userInfo.avatarUrl || ''
    };

    console.log('初始化临时用户信息:', tempUserInfo);

    this.setData({
      showEditor: true,
      tempUserInfo: tempUserInfo
    });
  },

  // 隐藏个人资料编辑器
  hideProfileEditor() {
    this.setData({
      showEditor: false
    });
  },

  // 监听昵称输入
  onNicknameInput(e) {
    console.log('昵称输入变化:', e.detail.value);
    this.setData({
      'tempUserInfo.nickName': e.detail.value
    });
  },

  // 保存个人资料修改
  saveProfileChanges() {
    // 防止重复提交
    if (this.data.isSaving) {
      return;
    }

    const { tempUserInfo } = this.data;

    // 验证昵称
    if (!tempUserInfo.nickName || tempUserInfo.nickName.trim() === '') {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    // 检查昵称长度
    if (tempUserInfo.nickName.trim().length > 20) {
      wx.showToast({
        title: '昵称不能超过20个字符',
        icon: 'none'
      });
      return;
    }

    // 检查是否有userId
    const userId = wx.getStorageSync('userId');
    if (!userId) {
      wx.showToast({
        title: '用户信息异常，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 设置保存状态
    this.setData({ isSaving: true });

    // 显示加载提示
    wx.showLoading({
      title: '保存中...'
    });

    // 调用后端更新接口
    this.callUpdateAPI(userId, tempUserInfo);
  },

  // 调用后端更新用户信息接口
  callUpdateAPI(userId, userInfo) {
    // 处理头像URL
    let avatarUrl = userInfo.avatarUrl || this.data.userInfo.avatarUrl || '';

    // 如果是微信临时文件，直接使用（实际项目中应该上传到服务器）
    if (avatarUrl.startsWith('wxfile://') || avatarUrl.startsWith('http://tmp')) {
      // 实际项目中，这里应该先上传头像到服务器，然后使用服务器返回的URL
      // 目前直接使用临时URL进行测试
    }

    const requestData = {
      userId: userId,
      nickname: userInfo.nickName.trim(),
      avatarUrl: avatarUrl
    };

    console.log('调用用户信息更新接口，请求数据:', requestData);

    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.wxUpdate}`,
      method: 'POST',
      data: requestData,
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('用户信息更新接口响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 0) {
          // 更新成功，保存到本地
          this.updateLocalUserInfo(userInfo);

          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
        } else {
          console.error('更新用户信息失败:', res.data);
          wx.hideLoading();
          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '保存失败，请重试',
            icon: 'none'
          });
        }

        // 重置保存状态
        this.setData({ isSaving: false });
      },
      fail: (error) => {
        console.error('用户信息更新接口调用失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });

        // 重置保存状态
        this.setData({ isSaving: false });
      }
    });
  },

  // 更新本地用户信息
  updateLocalUserInfo(newUserInfo) {
    // 更新本地存储的用户信息
    const currentUser = authManager.getCurrentUser();
    const updatedUser = {
      ...currentUser,
      nickName: newUserInfo.nickName,
      avatarUrl: newUserInfo.avatarUrl
    };

    console.log('更新本地用户信息:', updatedUser);

    // 保存到本地存储
    wx.setStorageSync('userInfo', updatedUser);

    // 更新authManager中的用户信息
    authManager.updateUserInfo(updatedUser);

    // 更新页面显示
    this.setData({
      userInfo: updatedUser,
      showEditor: false
    });
  },



  // 选择头像
  onChooseAvatar(e) {
    // 检查头像URL是否有效
    if (!e.detail.avatarUrl) {
      wx.showToast({
        title: '头像选择失败，请重试',
        icon: 'none'
      });
      return;
    }

    this.setData({
      'tempUserInfo.avatarUrl': e.detail.avatarUrl
    });

    wx.showToast({
      title: '头像已选择',
      icon: 'success',
      duration: 1000
    });
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites',
      fail: () => {
        wx.showToast({
          title: '收藏页面开发中',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings',
      fail: () => {
        wx.showToast({
          title: '设置页面开发中',
          icon: 'none'
        });
      }
    });
  },

  // 测试更新API（调试用）
  testUpdateAPI() {
    const userId = wx.getStorageSync('userId');
    if (!userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const testData = {
      nickName: '测试昵称' + Date.now(),
      avatarUrl: this.data.userInfo.avatarUrl || ''
    };

    console.log('测试更新API，userId:', userId, '测试数据:', testData);
    this.callUpdateAPI(userId, testData);
  }
})