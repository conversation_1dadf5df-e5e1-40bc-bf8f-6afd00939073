// pages/favorites/favorites.js
const apiConfig = require('../../config/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    favoriteRecipes: [],
    isLoading: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10,
    total: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadFavoriteRecipes(true);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载收藏列表
    this.loadFavoriteRecipes(true);
  },

  // 加载收藏列表
  loadFavoriteRecipes(refresh = false) {
    // 检查登录状态
    const userId = wx.getStorageSync('userId');
    if (!userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 如果是刷新，重置分页参数
    if (refresh) {
      this.setData({
        pageNum: 1,
        hasMore: true,
        favoriteRecipes: []
      });
    }

    // 防止重复加载
    if (this.data.isLoading || !this.data.hasMore) {
      return;
    }

    this.setData({ isLoading: true });

    const { pageNum, pageSize } = this.data;
    const url = `${apiConfig.baseUrl}${apiConfig.api.favoriteList}/${userId}?pageNum=${pageNum}&pageSize=${pageSize}`;

    console.log('请求收藏列表URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('收藏列表接口响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 0) {
          const responseData = res.data.data;
          const newRecipes = responseData.list || [];

          // 处理收藏列表数据
          const processedRecipes = newRecipes.map(item => ({
            id: item.id,
            name: item.name,
            difficultyLevel: item.difficultyLevel,
            difficultyLevelDesc: item.difficultyLevelDesc,
            favoriteTime: item.favoriteTime,
            // 添加显示用的字段
            emoji: this.getRecipeEmoji(item.name),
            difficulty: item.difficultyLevelDesc || this.getDifficultyText(item.difficultyLevel),
            isFavorite: true // 收藏列表中的都是已收藏的
          }));

          const currentRecipes = refresh ? [] : this.data.favoriteRecipes;
          const allRecipes = [...currentRecipes, ...processedRecipes];

          this.setData({
            favoriteRecipes: allRecipes,
            total: responseData.total || 0,
            hasMore: responseData.hasNext || false,
            pageNum: this.data.pageNum + 1,
            isLoading: false
          });

          console.log('收藏列表加载完成，总数:', allRecipes.length);
        } else {
          console.error('获取收藏列表失败:', res.data);
          this.setData({ isLoading: false });

          // 如果是第一页且失败，显示空状态
          if (refresh) {
            this.setData({ favoriteRecipes: [] });
          }

          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '获取收藏列表失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('收藏列表接口调用失败:', error);
        this.setData({ isLoading: false });

        // 如果是第一页且失败，显示空状态
        if (refresh) {
          this.setData({ favoriteRecipes: [] });
        }

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取菜谱表情图标（与首页保持一致）
  getRecipeEmoji(recipeName) {
    if (!recipeName) return '🍽️';

    const name = recipeName.toLowerCase();
    if (name.includes('面') || name.includes('noodle')) return '🍜';
    if (name.includes('汤') || name.includes('soup')) return '🍲';
    if (name.includes('饭') || name.includes('rice')) return '🍚';
    if (name.includes('鸡') || name.includes('chicken')) return '🍲';
    if (name.includes('鱼') || name.includes('fish')) return '🐟';
    if (name.includes('肉') || name.includes('meat')) return '🥩';
    if (name.includes('蛋') || name.includes('egg')) return '🥚';
    if (name.includes('菜') || name.includes('vegetable')) return '🥬';
    if (name.includes('虾') || name.includes('shrimp')) return '🦐';
    if (name.includes('蟹') || name.includes('crab')) return '🦀';
    if (name.includes('牛') || name.includes('beef')) return '🐄';
    if (name.includes('猪') || name.includes('pork')) return '🐷';
    if (name.includes('羊') || name.includes('lamb')) return '🐑';
    if (name.includes('豆腐') || name.includes('tofu')) return '🧈';
    if (name.includes('粥') || name.includes('porridge')) return '🥣';
    if (name.includes('饺子') || name.includes('dumpling')) return '🥟';
    if (name.includes('包子') || name.includes('bun')) return '🥟';
    return '🍽️'; // 默认图标
  },

  // 根据难度等级获取文字描述
  getDifficultyText(level) {
    const difficultyMap = {
      1: '简单',
      2: '中等',
      3: '困难'
    };
    return difficultyMap[level] || '中等';
  },

  // 取消收藏
  toggleFavorite(e) {
    const { id } = e.currentTarget.dataset;
    const userId = wx.getStorageSync('userId');

    if (!userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消收藏',
      content: '确定要取消收藏这个菜谱吗？',
      success: (res) => {
        if (res.confirm) {
          this.removeFavorite(userId, id);
        }
      }
    });
  },

  // 调用取消收藏接口
  removeFavorite(userId, recipeId) {
    wx.showLoading({
      title: '取消收藏中...'
    });

    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.favoriteRemove}`,
      method: 'POST',
      data: {
        userId: userId,
        recipeId: recipeId
      },
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('取消收藏接口响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 0) {
          // 取消收藏成功，从列表中移除
          const { favoriteRecipes } = this.data;
          const updatedRecipes = favoriteRecipes.filter(item => item.id != recipeId);

          this.setData({
            favoriteRecipes: updatedRecipes,
            total: this.data.total - 1
          });

          wx.showToast({
            title: '已取消收藏',
            icon: 'success'
          });
        } else {
          console.error('取消收藏失败:', res.data);
          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '取消收藏失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('取消收藏接口调用失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到菜谱详情
  goToRecipeDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/recipe-detail/recipe-detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '菜谱详情页开发中',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * scroll-view滚动到底部事件处理函数
   */
  onScrollToLower() {
    console.log('滚动到底部，加载更多收藏');
    this.loadFavoriteRecipes(false);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('触底加载更多收藏');
    this.loadFavoriteRecipes(false);
  },

  /**
   * 用户下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新收藏列表');
    this.loadFavoriteRecipes(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { favoriteRecipes } = this.data;
    let title = '我的收藏菜谱 - 智能烹饪助手';

    if (favoriteRecipes && favoriteRecipes.length > 0) {
      title = `我收藏了${favoriteRecipes.length}个美味菜谱 - 智能烹饪助手`;
    }

    return {
      title: title,
      path: '/pages/index/index',
      imageUrl: '',
      success: function(res) {
        console.log('分享成功', res);
      },
      fail: function(res) {
        console.log('分享失败', res);
      }
    };
  }
});
