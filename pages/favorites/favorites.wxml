<!--pages/favorites/favorites.wxml-->
<navigation-bar title="收藏" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="onScrollToLower" lower-threshold="100">
  <view class="container">
    <view class="favorites-header-spacer"></view>

    <!-- 收藏列表 -->
    <view class="favorites-list" wx:if="{{favoriteRecipes.length > 0}}">
      <view class="favorite-item" wx:for="{{favoriteRecipes}}" wx:key="id" bindtap="goToRecipeDetail" data-id="{{item.id}}">
        <view class="favorite-image">
          <text class="recipe-emoji">{{item.emoji || '🍽️'}}</text>
        </view>
        <view class="favorite-info">
          <text class="favorite-name">{{item.name}}</text>
          <view class="favorite-meta">
            <text class="favorite-difficulty">{{item.difficultyLevelDesc || item.difficulty}}</text>
            <text class="favorite-time" wx:if="{{item.favoriteTime}}">收藏于 {{item.favoriteTime}}</text>
          </view>
        </view>
        <view class="favorite-btn" bindtap="toggleFavorite" data-id="{{item.id}}" catchtap="true">
          <text class="favorite-icon">♥</text>
        </view>
      </view>
    </view>

    <!-- 加载更多状态 -->
    <view class="loading-more" wx:if="{{isLoading && favoriteRecipes.length > 0}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && favoriteRecipes.length > 0}}">
      <text class="no-more-text">没有更多收藏了</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{!isLoading && favoriteRecipes.length === 0}}" class="empty-state">
      <text class="empty-icon">♡</text>
      <text class="empty-title">暂无收藏</text>
      <text class="empty-desc">快去收藏你喜欢的菜谱吧</text>
      <button class="go-explore-btn" bindtap="goToHome">去发现美食</button>
    </view>

    <!-- 首次加载状态 -->
    <view wx:elif="{{isLoading && favoriteRecipes.length === 0}}" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</scroll-view>
