// index.js
const apiConfig = require('../../config/api.js')
const authManager = require('../../utils/auth.js')

Page({
  data: {
    recommendRecipes: [],
    categories: [
      {
        id: 1,
        name: '蔬菜',
        icon: '🥦'
      },
      {
        id: 2,
        name: '肉类',
        icon: '🍖'
      },
      {
        id: 3,
        name: '海鲜',
        icon: '🦐'
      },
      {
        id: 4,
        name: '主食',
        icon: '🍚'
      },
      {
        id: 5,
        name: '水果',
        icon: '🍎'
      },
      {
        id: 6,
        name: '调味料',
        icon: '🧂'
      },
      {
        id: 7,
        name: '豆制品',
        icon: '🌱'
      },
      {
        id: 8,
        name: '更多',
        icon: '➕'
      }
    ]
  },

  onLoad() {
    // 页面加载时请求推荐菜谱数据
    this.loadRecommendRecipes()
  },

  // 加载推荐菜谱
  loadRecommendRecipes() {
    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.searchRecipesByRandom}`,
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('随机菜谱接口响应:', res)
        if (res.statusCode === 200 && res.data.code === 0) {
          const recipes = res.data.data.list || [];
          console.log('推荐菜谱数据:', recipes);
          console.log('第一个菜谱数据:', recipes[0]);
          if (recipes[0]) {
            console.log('第一个菜谱的id:', recipes[0].id);
            console.log('第一个菜谱的id类型:', typeof recipes[0].id);
          }
          this.setData({
            recommendRecipes: recipes
          })
        } else {
          console.error('获取推荐菜谱失败:', res.data)
          wx.showToast({
            title: '获取推荐菜谱失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('请求推荐菜谱失败:', err)
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      }
    })
  },

  // 导航到拍照页面
  navigateToCamera() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      this.showLoginConfirmDialog();
      return;
    }

    // 已登录，直接跳转到拍照页面
    wx.navigateTo({
      url: '/pages/camera/camera'
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    // 恢复登录状态
    authManager.restoreLoginState();

    // 检查是否已登录
    const isLoggedIn = authManager.checkLoginStatus();
    const userId = wx.getStorageSync('userId');

    console.log('首页检查登录状态:', {
      isLoggedIn: isLoggedIn,
      hasUserId: !!userId,
      userInfo: authManager.getCurrentUser()
    });

    return isLoggedIn && userId;
  },

  // 显示登录确认对话框
  showLoginConfirmDialog(customMessage) {
    const defaultMessage = '拍照识别食材功能需要登录后才能使用，是否前往登录？';
    const message = customMessage || defaultMessage;

    wx.showModal({
      title: '需要登录',
      content: message,
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户确认登录，跳转到个人页面
          wx.switchTab({
            url: '/pages/profile/profile',
            success: () => {
              // 可以通过事件或其他方式通知个人页面需要登录
              wx.showToast({
                title: '请先完成登录',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      }
    });
  },

  // 导航到菜谱列表页
  navigateToRecipe() {
    wx.navigateTo({
      url: '/pages/recipe/recipe'
    })
  },

  // 导航到菜谱详情页
  navigateToDetail(e) {
    console.log('点击菜谱，事件对象:', e);
    console.log('currentTarget:', e.currentTarget);
    console.log('dataset:', e.currentTarget.dataset);

    const { id } = e.currentTarget.dataset;
    console.log('提取的菜谱ID:', id);
    console.log('ID类型:', typeof id);

    if (!id || id === 'null' || id === 'undefined') {
      console.error('菜谱ID无效:', id);
      wx.showToast({
        title: '菜谱数据异常',
        icon: 'none'
      });
      return;
    }

    const url = `/pages/recipe-detail/recipe-detail?id=${id}`;
    console.log('跳转URL:', url);

    wx.navigateTo({
      url: url
    });
  },

  // 导航到分类页面
  navigateToCategory(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe/recipe?category=${id}`
    })
  },

  // 搜索功能
  onSearch(e) {
    const keyword = e && e.detail && e.detail.value
    if (keyword) {
      wx.navigateTo({
        url: `/pages/recipe/recipe?keyword=${encodeURIComponent(keyword)}`
      })
    } else {
      // 如果没有输入，跳转到搜索页（空关键词的搜索模式）
      wx.navigateTo({
        url: '/pages/recipe/recipe?keyword='
      })
    }
  },

  // 跳转到收藏页面
  navigateToFavorite() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      this.showLoginConfirmDialog('查看收藏功能需要登录后才能使用，是否前往登录？');
      return;
    }

    // 已登录，跳转到收藏页面
    wx.navigateTo({
      url: '/pages/favorites/favorites',
      fail: () => {
        wx.showToast({
          title: '收藏页面开发中',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '智能烹饪助手 - 拍照识别食材，推荐美味菜谱',
      path: '/pages/index/index',
      imageUrl: '', // 可以设置分享图片
      success: function(res) {
        console.log('分享成功', res);
      },
      fail: function(res) {
        console.log('分享失败', res);
      }
    };
  }
})
