// API配置
const config = {
  // 开发环境
  development: {
    baseUrl: 'https://imgism.cn',
    api: {
      // 食材识别接口 - 支持Base64格式的多文件上传
      // 请求格式: POST /cook/upload/photos
      // 参数: { userId: number, files: [{ fileName, fileId, fileSize, fileData }] }
      // fileData格式: "data:image/jpeg;base64,/9j/4AAQ..."
      uploadPhotos: '/cook/upload/photos',

      // 根据食材搜索菜谱接口
      // 请求格式: GET /recipe/search-by-ingredients
      // 参数: ingredientNames, pageNum, pageSize
      searchRecipesByIngredients: '/recipe/search-by-ingredients',

      // 获取菜谱详情接口
      // 请求格式: GET /recipe/detail/{userId}/{recipeId}
      // 路径参数: userId - 用户ID, recipeId - 菜谱ID
      recipeDetail: '/recipe/detail',

      // 微信小程序登录接口
      // 请求格式: POST /user/wx-login
      // 参数: { code, userInfo }
      wxLogin: '/user/wx-login',

      // 随机获取菜谱列表接口
      // 请求格式: GET /recipe/search-by-random
      // 无需参数，返回5条随机菜谱
      searchRecipesByRandom: '/recipe/search-by-random',

      // 分页查询历史记录接口
      // 请求格式: POST /cook/version-records/page
      // 参数: { userId, pageNum, pageSize }
      historyRecordsPage: '/cook/version-records/page',

      // 更新用户信息接口
      // 请求格式: POST /user/wx-update
      // 参数: { userId, nickname, avatarUrl }
      wxUpdate: '/user/wx-update',

      // 收藏菜谱接口
      // 请求格式: POST /favorite/add
      // 参数: { userId, recipeId }
      favoriteAdd: '/favorite/add',

      // 取消收藏菜谱接口
      // 请求格式: POST /favorite/remove
      // 参数: { userId, recipeId }
      favoriteRemove: '/favorite/remove',

      // 获取收藏菜谱列表接口
      // 请求格式: GET /favorite/list/{userId}
      // 查询参数: pageNum, pageSize
      favoriteList: '/favorite/list',

      // 在这里添加其他接口路径
      // example: '/cook/other/api'
    }
  },
  // 生产环境
  production: {
    baseUrl: 'https://imgism.cn', // 替换为实际的生产环境域名
    api: {
      // 食材识别接口 - 支持Base64格式的多文件上传
      // 请求格式: POST /cook/upload/photos
      // 参数: { userId: number, files: [{ fileName, fileId, fileSize, fileData }] }
      // fileData格式: "data:image/jpeg;base64,/9j/4AAQ..."
      uploadPhotos: '/cook/upload/photos',

      // 根据食材搜索菜谱接口
      // 请求格式: GET /recipe/search-by-ingredients
      // 参数: ingredientNames, pageNum, pageSize
      searchRecipesByIngredients: '/recipe/search-by-ingredients',

      // 获取菜谱详情接口
      // 请求格式: GET /recipe/detail/{userId}/{recipeId}
      // 路径参数: userId - 用户ID, recipeId - 菜谱ID
      recipeDetail: '/recipe/detail',

      // 微信小程序登录接口
      // 请求格式: POST /user/wx-login
      // 参数: { code, userInfo }
      wxLogin: '/user/wx-login',

      // 随机获取菜谱列表接口
      // 请求格式: GET /recipe/search-by-random
      // 无需参数，返回5条随机菜谱
      searchRecipesByRandom: '/recipe/search-by-random',

      // 分页查询历史记录接口
      // 请求格式: POST /cook/version-records/page
      // 参数: { userId, pageNum, pageSize }
      historyRecordsPage: '/cook/version-records/page',

      // 更新用户信息接口
      // 请求格式: POST /user/wx-update
      // 参数: { userId, nickname, avatarUrl }
      wxUpdate: '/user/wx-update',

      // 收藏菜谱接口
      // 请求格式: POST /favorite/add
      // 参数: { userId, recipeId }
      favoriteAdd: '/favorite/add',

      // 取消收藏菜谱接口
      // 请求格式: POST /favorite/remove
      // 参数: { userId, recipeId }
      favoriteRemove: '/favorite/remove',

      // 获取收藏菜谱列表接口
      // 请求格式: GET /favorite/list/{userId}
      // 查询参数: pageNum, pageSize
      favoriteList: '/favorite/list',
    }
  }
};

// 获取当前环境
// 微信小程序中，可以通过 __wxConfig 判断环境
const env = __wxConfig.envVersion === 'release' ? 'production' : 'development';

// 导出当前环境的配置
module.exports = {
  baseUrl: config[env].baseUrl,
  api: config[env].api,
  // 导出当前环境，方便其他地方使用
  env: env
}; 