# 拍照上传接口userId字段更新

## 📋 更新概述

根据后端接口要求，为 `/cook/upload/photos` 接口添加了 `userId` 字段，用于标识当前用户的登录ID。

## ✅ 完成的修改

### 1. 更新请求参数格式

**修改前:**
```json
{
  "files": [
    {
      "fileName": "photo_123456.jpg",
      "fileId": 123456,
      "fileSize": 1024000,
      "fileData": "data:image/jpeg;base64,/9j/4AAQ..."
    }
  ]
}
```

**修改后:**
```json
{
  "userId": 1,
  "files": [
    {
      "fileName": "photo_123456.jpg", 
      "fileId": 123456,
      "fileSize": 1024000,
      "fileData": "data:image/jpeg;base64,/9j/4AAQ..."
    }
  ]
}
```

### 2. 修改的文件列表

#### 2.1 `pages/camera/camera.js`
- **`uploadAndRecognize()` 方法**: 添加userId参数（从相册选择单张图片）
- **`uploadPhotos()` 方法**: 添加userId参数（多张照片上传）
- 添加登录状态检查，未登录时提示用户先登录

#### 2.2 `pages/recipe/recipe.js`
- **`fetchRecognitionResult()` 方法**: 添加userId参数
- 添加登录状态检查

#### 2.3 `config/api.js`
- 更新接口注释，反映新的参数格式
- 开发环境和生产环境配置都已更新

### 3. 更新的文档

#### 3.1 `docs/API-Integration.md`
- 更新请求参数示例
- 添加userId字段说明
- 更新代码示例

#### 3.2 `docs/API-Integration-Summary.md`
- 更新接口参数格式说明
- 更新代码示例

#### 3.3 `utils/api-test.js`
- 更新测试数据格式，包含userId字段

## 🔧 关键代码变更

### 登录状态检查
```javascript
// 获取当前用户ID
const userId = wx.getStorageSync('userId');
if (!userId) {
  wx.showToast({
    title: '请先登录',
    icon: 'none'
  });
  return;
}
```

### 请求数据构造
```javascript
const uploadData = {
  userId: userId,
  files: files
};
```

## 🚀 功能影响

### 正面影响
1. **用户关联**: 上传的照片现在与具体用户关联
2. **数据追踪**: 后端可以记录每个用户的上传历史
3. **权限控制**: 确保只有登录用户才能使用拍照识别功能

### 注意事项
1. **登录依赖**: 用户必须先登录才能使用拍照功能
2. **错误处理**: 未登录时会提示用户先登录
3. **向后兼容**: 保持了原有的files数组结构

## 📱 用户体验

- 未登录用户尝试拍照时会收到"请先登录"的提示
- 登录用户的使用体验保持不变
- 所有拍照识别功能（相机拍照、相册选择）都已支持userId参数

## 🔍 测试建议

1. **登录状态测试**: 验证未登录时的提示功能
2. **拍照功能测试**: 确认登录后拍照功能正常
3. **相册选择测试**: 确认从相册选择图片功能正常
4. **接口参数测试**: 验证userId正确传递到后端

## 📝 后续工作

如果后端需要其他用户相关的参数，可以参考本次修改的模式进行扩展。
