# API对接文档 - 食材识别接口

## 接口概述

**接口名称**: 食材识别上传接口  
**接口地址**: `POST /cook/upload/photos`  
**功能描述**: 支持Base64格式的多文件上传，用于食材识别  

## 请求格式

### 请求头
```
Content-Type: application/json
Accept: application/json
```

### 请求参数
```json
{
  "userId": 1,                         // 用户ID（必填，数字类型）
  "files": [
    {
      "fileName": "image1.jpg",        // 文件名（必填）
      "fileId": 123456,                // 文件ID（可选，数字类型）
      "fileSize": 1024000,             // 文件大小，单位字节（必填，必须大于0）
      "fileData": "data:image/jpeg;base64,/9j/4AAQ..."  // Base64编码的文件数据（必填）
    }
  ]
}
```

### 参数说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| files | Array | 是 | 文件数组 |
| files[].fileName | String | 是 | 文件名，建议包含扩展名 |
| files[].fileId | Number | 否 | 文件ID，可用于标识文件 |
| files[].fileSize | Number | 是 | 文件大小（字节），必须大于0 |
| files[].fileData | String | 是 | 完整的Data URL格式的Base64数据 |

## 代码实现

### 1. 相机页面上传实现

在 `pages/camera/camera.js` 中的关键方法：

```javascript
// 上传照片
async uploadPhotos() {
  const photos = this.data.photos;
  const files = [];
  
  for (let i = 0; i < photos.length; i++) {
    const photo = photos[i];
    
    // 获取文件信息
    const fileInfo = await this.getFileInfo(photo.path);
    const base64 = await this.fileToBase64(photo.path);
    
    files.push({
      fileName: `photo_${photo.timestamp}.jpg`,
      fileId: photo.timestamp, // 使用时间戳作为文件ID
      fileSize: fileInfo.size, // 使用实际文件大小
      fileData: `data:image/jpeg;base64,${base64}` // 完整的Data URL格式
    });
  }
  
  // 获取用户ID
  const userId = wx.getStorageSync('userId');
  if (!userId) {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 发送请求
  const uploadData = {
    userId: userId,
    files: files
  };
  wx.request({
    url: `${apiConfig.baseUrl}${apiConfig.api.uploadPhotos}`,
    method: 'POST',
    data: uploadData,
    header: {
      'content-type': 'application/json'
    },
    success: (res) => {
      // 处理成功响应
    },
    fail: (error) => {
      // 处理失败
    }
  });
}
```

### 2. 辅助方法

```javascript
// 获取文件信息
getFileInfo(filePath) {
  return new Promise((resolve, reject) => {
    wx.getFileInfo({
      filePath: filePath,
      success: (res) => resolve(res),
      fail: (error) => reject(error)
    });
  });
}

// 文件转base64
fileToBase64(filePath) {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readFile({
      filePath: filePath,
      encoding: 'base64',
      success: (res) => resolve(res.data),
      fail: (error) => reject(error)
    });
  });
}
```

## 重要注意事项

### 1. fileData格式要求
- **错误格式**: 直接的base64字符串 `"/9j/4AAQSkZJRgABAQ..."`
- **正确格式**: 完整的Data URL `"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQ..."`

### 2. fileSize获取
- 必须使用 `wx.getFileInfo()` 获取真实文件大小
- 不能使用默认值或估算值

### 3. 错误处理
- 网络请求失败
- 文件读取失败
- Base64转换失败
- 服务器响应错误

### 4. 性能优化
- 大文件上传时显示进度条
- 支持多文件批量上传
- 合理的超时设置

## 测试验证

使用 `utils/api-test.js` 中的工具进行测试：

```javascript
const apiTest = require('../utils/api-test.js');
const apiConfig = require('../config/api.js');

// 验证数据格式
const testData = apiTest.createTestUploadData();
const validation = apiTest.validateUploadData(testData);

if (validation.valid) {
  // 发送测试请求
  apiTest.testUploadPhotosAPI(apiConfig);
} else {
  console.error('数据格式错误:', validation.errors);
}
```

## 根据食材搜索菜谱接口

### 接口信息
**接口地址**: `GET /recipe/search-by-ingredients`
**功能描述**: 根据食材名称快速搜索相关菜谱

### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| ingredientNames | String | 是 | 食材名称，多个用逗号分隔 |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |

### 响应格式
```json
{
    "code": 0,
    "msg": "成功",
    "data": {
        "list": [
            {
                "id": 1,
                "name": "包菜炒鸡蛋粉丝",
                "description": "包菜炒鸡蛋粉丝，是中国的一道日常生活中所熟知的菜品",
                "difficultyLevel": 3,
                "difficultyLevelDesc": "困难",
                "servings": 1
            }
        ],
        "total": 1,
        "pageNum": 1,
        "pageSize": 10,
        "pages": 1,
        "hasNext": false,
        "hasPrevious": false
    }
}
```

### 代码实现

在 `pages/recipe/recipe.js` 中的实现：

```javascript
// 根据食材搜索菜谱
searchRecipesByIngredients(ingredientNames, pageNum = 1, pageSize = 10) {
  const apiConfig = require('../../config/api.js');

  // 构建请求URL
  const url = `${apiConfig.baseUrl}${apiConfig.api.searchRecipesByIngredients}?ingredientNames=${encodeURIComponent(ingredientNames)}&pageNum=${pageNum}&pageSize=${pageSize}`;

  wx.request({
    url: url,
    method: 'GET',
    header: {
      'content-type': 'application/json'
    },
    success: (res) => {
      if (res.statusCode === 200 && res.data && res.data.code === 0) {
        const recipeData = res.data.data;
        // 处理响应数据
        this.setData({
          recipes: [...this.data.recipes, ...recipeData.list],
          hasMore: recipeData.hasNext
        });
      }
    }
  });
}
```

## 环境配置

在 `config/api.js` 中配置不同环境的接口地址：

```javascript
const config = {
  development: {
    baseUrl: 'http://**************:8080',
    api: {
      uploadPhotos: '/cook/upload/photos',
      searchRecipesByIngredients: '/recipe/search-by-ingredients'
    }
  },
  production: {
    baseUrl: 'https://api.yourdomain.com',
    api: {
      uploadPhotos: '/cook/upload/photos',
      searchRecipesByIngredients: '/recipe/search-by-ingredients'
    }
  }
};
```
