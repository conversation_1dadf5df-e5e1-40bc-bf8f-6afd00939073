<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜谱详情页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #f5f5f5;
            height: 100vh;
            overflow-y: auto;
        }
        
        /* 导航栏 */
        .custom-navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 44px 16px 10px 16px;
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }
        
        .nav-back {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            background: #f5f5f5;
            color: #333;
        }

        .nav-placeholder {
            width: 30px;
            height: 30px;
        }
        
        .nav-title {
            flex: 1;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 10px;
        }
        
        /* 主要信息卡片 */
        .recipe-main-card {
            background: #fff;
            padding: 16px;
            margin: 10px 8px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
            display: flex;
            gap: 12px;
        }
        
        .recipe-image {
            width: 80px;
            height: 80px;
            background: #ffeaa7;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 40px;
            flex-shrink: 0;
        }
        
        .recipe-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .recipe-name {
            font-size: 18px;
            font-weight: 600;
            color: #222;
            margin-bottom: 8px;
        }
        
        .recipe-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #666;
        }
        
        .recipe-stats {
            display: flex;
            gap: 10px;
            flex-wrap: nowrap;
            align-items: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 2px;
            font-size: 11px;
            color: #666;
            flex-shrink: 0;
            white-space: nowrap;
        }
        
        /* 区块样式 */
        .section {
            background: #fff;
            padding: 16px;
            margin: 10px 8px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #222;
            margin-bottom: 12px;
        }
        
        /* 食材列表 */
        .ingredients-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .ingredient-item {
            display: flex;
            align-items: center;
            padding: 12px 10px;
            border-radius: 6px;
            gap: 8px;
        }
        
        .ingredient-item.has {
            background: #e8f5e8;
        }
        
        .ingredient-item.missing {
            background: #ffeaea;
        }
        
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid;
        }
        
        .status-icon.has {
            color: #4caf50;
            border-color: #4caf50;
            background: #e8f5e8;
        }
        
        .status-icon.missing {
            color: #f44336;
            border-color: #f44336;
            background: #ffeaea;
        }
        
        .ingredient-name {
            flex: 1;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .quantity-text {
            font-size: 13px;
            color: #666;
        }
        
        /* 营养信息 */
        .nutrition-container {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }
        
        .nutrition-item {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .nutrition-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .nutrition-value.calories { color: #ff6b35; }
        .nutrition-value.protein { color: #4a90e2; }
        .nutrition-value.carbs { color: #4caf50; }
        .nutrition-value.fat { color: #9c27b0; }
        
        .nutrition-label {
            font-size: 12px;
            color: #666;
        }
        
        /* 制作步骤 */
        .steps-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .step-item {
            display: flex;
            gap: 10px;
            align-items: flex-start;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .step-number {
            width: 28px;
            height: 28px;
            background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
            border-radius: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .step-text {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
            flex: 1;
        }
        
        .step-expand {
            width: 24px;
            height: 24px;
            background: #fff;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            margin-left: 8px;
            font-size: 12px;
            color: #666;
            font-weight: 600;
        }

        .bottom-spacing {
            height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="custom-navbar">
            <div class="nav-back">←</div>
            <div class="nav-title">西红柿鸡蛋面</div>
            <div class="nav-placeholder"></div>
        </div>
        
        <!-- 主要信息卡片 -->
        <div class="recipe-main-card">
            <div class="recipe-image">🍜</div>
            <div class="recipe-info">
                <div class="recipe-name">西红柿鸡蛋面</div>
                
                <div class="recipe-stats">
                    <div class="stat-item">⭐⭐⭐</div>
                    <div class="stat-item">👥 1人做过</div>
                </div>
            </div>
        </div>
        
        <!-- 所需食材 -->
        <div class="section">
            <div class="section-title">所需食材</div>
            <div class="ingredients-container">
                <div class="ingredient-item has">
                    <div class="status-icon has">✓</div>
                    <div class="ingredient-name">西红柿</div>
                    <div class="quantity-text">适量</div>
                </div>
                <div class="ingredient-item has">
                    <div class="status-icon has">✓</div>
                    <div class="ingredient-name">鸡蛋</div>
                    <div class="quantity-text">适量</div>
                </div>
                <div class="ingredient-item missing">
                    <div class="status-icon missing">✗</div>
                    <div class="ingredient-name">面条</div>
                    <div class="quantity-text">适量</div>
                </div>
                <div class="ingredient-item has">
                    <div class="status-icon has">✓</div>
                    <div class="ingredient-name">大葱</div>
                    <div class="quantity-text">适量</div>
                </div>
                <div class="ingredient-item missing">
                    <div class="status-icon missing">✗</div>
                    <div class="ingredient-name">盐</div>
                    <div class="quantity-text">适量</div>
                </div>
                <div class="ingredient-item missing">
                    <div class="status-icon missing">✗</div>
                    <div class="ingredient-name">糖</div>
                    <div class="quantity-text">适量</div>
                </div>
            </div>
        </div>
        
        <!-- 营养信息 -->
        <div class="section">
            <div class="section-title">营养信息</div>
            <div class="nutrition-container">
                <div class="nutrition-item">
                    <span class="nutrition-value calories">320</span>
                    <span class="nutrition-label">卡路里</span>
                </div>
                <div class="nutrition-item">
                    <span class="nutrition-value protein">18g</span>
                    <span class="nutrition-label">蛋白质</span>
                </div>
                <div class="nutrition-item">
                    <span class="nutrition-value carbs">45g</span>
                    <span class="nutrition-label">碳水</span>
                </div>
                <div class="nutrition-item">
                    <span class="nutrition-value fat">8g</span>
                    <span class="nutrition-label">脂肪</span>
                </div>
            </div>
        </div>
        
        <!-- 制作步骤 -->
        <div class="section">
            <div class="section-title">制作步骤</div>
            <div class="steps-list">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-text">准备食材：西红柿切块，鸡蛋打散，大葱切段</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-text">热锅下油，倒入蛋液炒熟盛起备用</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-text">锅内留底油，下西红柿块炒出汁水</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-text">加入适量盐和糖调味</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <div class="step-text">倒入炒蛋翻炒均匀</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <div class="step-text">煮面条至8分熟，倒入锅中</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">7</div>
                    <div class="step-content">
                        <div class="step-text">加入适量面汤，焖煮2分钟</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">8</div>
                    <div class="step-content">
                        <div class="step-text">撒上葱花即可出锅</div>
                        <div class="step-expand">+</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部间距 -->
        <div class="bottom-spacing"></div>
    </div>
</body>
</html>
